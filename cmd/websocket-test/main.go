package main

import (
	"fmt"
	"log"
	"time"

	"poc-checker/pkg/webclient"
)

func main() {
	fmt.Println("WebSocket客户端测试")
	
	// 创建客户端配置
	config := webclient.DefaultConfig()
	config.ConnectTimeout = 10 * time.Second
	config.ReadTimeout = 30 * time.Second
	config.WriteTimeout = 5 * time.Second
	
	// 创建客户端
	client := webclient.New(config)
	
	// 设置消息处理器
	client.SetMessageHandler(webclient.MessageHandler{
		OnMessage: func(data []byte) {
			fmt.Printf("✅ 收到消息: %s\n", string(data))
		},
		OnError: func(err error) {
			fmt.Printf("❌ 错误: %v\n", err)
		},
		OnClose: func() {
			fmt.Println("🔌 连接已关闭")
		},
	})
	
	// 连接到echo服务器
	fmt.Println("🔗 正在连接到 wss://echo.websocket.org...")
	err := client.ConnectWithRetry("wss://echo.websocket.org")
	if err != nil {
		log.Printf("连接失败: %v", err)
		return
	}
	defer client.Close()
	
	fmt.Println("✅ 连接成功!")
	
	// 发送测试消息
	messages := []string{
		"Hello WebSocket!",
		"测试中文消息",
		"Test message 3",
		"Final test message",
	}
	
	for i, msg := range messages {
		fmt.Printf("📤 发送消息 %d: %s\n", i+1, msg)
		err = client.SendText(msg)
		if err != nil {
			log.Printf("发送消息失败: %v", err)
			continue
		}
		
		// 等待响应
		time.Sleep(1 * time.Second)
	}
	
	// 测试ping
	fmt.Println("🏓 发送ping...")
	err = client.Ping()
	if err != nil {
		log.Printf("Ping失败: %v", err)
	}
	
	// 等待一段时间接收所有响应
	fmt.Println("⏳ 等待响应...")
	time.Sleep(3 * time.Second)
	
	fmt.Println("✅ 测试完成!")
}
