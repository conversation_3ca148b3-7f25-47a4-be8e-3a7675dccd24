package main

import (
	"fmt"
	"log/slog"
	"os"

	"github.com/alecthomas/kingpin/v2"
	"gopkg.in/yaml.v3"

	"poc-checker/pkg/config"
	"poc-checker/pkg/fofa"
)

var (
	fingerprint = kingpin.Flag("fingerprint", "fofa指纹特征字符串").Short('f').String()
	configFile  = kingpin.Flag("config", "配置文件").Short('c').Default("config.yaml").String()
)

func main() {
	logger := slog.New(config.MakeHandler(os.Stdout))
	var cfg config.Config
	yamlFile, err := os.ReadFile(*configFile)
	if err != nil {
		logger.Error("配置文件读取失败", slog.Any("err", err))
		return
	}
	if err = yaml.Unmarshal(yamlFile, &cfg); err != nil {
		logger.Error("yaml反序列化失败", slog.Any("err", err))
		return
	}
	kingpin.Parse()
	fc := fofa.New(cfg.FofaKey)
	if len(*fingerprint) == 0 {
		logger.Error("fingerprint is empty")
		os.Exit(1)
	}
	r, err := fc.Search(*fingerprint, 1000)
	if err != nil {
		logger.Error("fofa搜索失败", slog.Any("err", err))
		return
	}
	for _, result := range r.Results {

	}
}
