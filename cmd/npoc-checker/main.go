package main

import (
	"errors"
	"fmt"
	"log/slog"
	"os"
	"sync"
	"sync/atomic"
	"time"

	"gopkg.in/yaml.v3"

	"poc-checker/pkg/config"
	"poc-checker/pkg/fofa"
	"poc-checker/pkg/npoc"
)

func main() {
	t1 := time.Now()
	var cfg config.Config
	yamlFile, err := os.ReadFile("cfg.yaml")
	if err != nil {
		slog.Error(err.Error())
		os.Exit(1)
	}
	err = yaml.Unmarshal(yamlFile, &cfg)
	if err != nil {
		slog.Error(err.Error())
		os.Exit(1)
	}
	template := os.Args[1]
	query, err := getFofaQuery(template)
	if err != nil {
		fmt.Println(err)
		return
	}

	func() {
		fc := fofa.New(cfg.Fofa<PERSON>ey)
		r, err := fc.Search(query, 1000)
		if err != nil {
			fmt.Println(err)
			return
		}
		wg := sync.WaitGroup{}
		sem := make(chan struct{}, 80)
		var count atomic.Int32
		var hint atomic.Int32
		for _, site := range r.Results {
			sem <- struct{}{}
			go func() {
				defer func() {
					<-sem
					wg.Done()
				}()
				wg.Add(1)
				count.Add(1)
				err := npoc.NewNpoc().Exe(cfg.Npoc).Template(template).Url(site.Host).Proxy(cfg.Proxy).Run()
				if err == nil {
					hint.Add(1)
				}
			}()
		}
		wg.Wait()
		fmt.Println("fofa 扫描", count.Load())
		fmt.Println("fofa poc命中", hint.Load())
	}()

	//shodanQuery, _ := getShodanQuery(template)
	//func() {
	//	qc := quake.New(cfg.QuakeKey)
	//	r, err := qc.Search(shodanQuery, 1000)
	//	if err != nil {
	//		fmt.Println(err)
	//		return
	//	}
	//	wg := sync.WaitGroup{}
	//	sem := make(chan struct{}, 80)
	//	var count atomic.Int32
	//	var hint atomic.Int32
	//	for _, data := range r.Data {
	//		sem <- struct{}{}
	//		go func() {
	//			defer func() {
	//				<-sem
	//				wg.Done()
	//			}()
	//			count.Add(1)
	//			wg.Add(1)
	//			url := fmt.Sprintf("%s:%d", data.Hostname, data.Port)
	//			if data.Hostname == "" {
	//				url = fmt.Sprintf("%s:%d", data.Ip, data.Port)
	//			}
	//			err = npoc.NewNpoc().Exe(cfg.Npoc).Template(template).Url(fmt.Sprintf("http://%s", url)).Proxy(cfg.Proxy).Run()
	//			if err == nil {
	//				hint.Add(1)
	//				return
	//			}
	//			err = npoc.NewNpoc().Exe(cfg.Npoc).Template(template).Url(fmt.Sprintf("https://%s", url)).Proxy(cfg.Proxy).Run()
	//			if err == nil {
	//				hint.Add(1)
	//				return
	//			}
	//		}()
	//	}
	//	wg.Wait()
	//	fmt.Println("quake 扫描", count.Load())
	//	fmt.Println("quake poc命中", hint.Load())
	//}()

	elapsedTime := time.Since(t1)
	fmt.Printf("耗时：%s\n", elapsedTime)
}

func getFofaQuery(f string) (string, error) {
	t, err := os.ReadFile(f)
	if err != nil {
		return "", err
	}
	var tp Template
	err = yaml.Unmarshal(t, &tp)
	if err != nil {
		return "", err
	}
	fofaQuery := func() string {
		if q, ok := tp.Info.Metadata.FofaQuery.(string); ok {
			return q
		}
		if q, ok := tp.Info.Metadata.FofaQuery.([]interface{}); ok {
			if query, ok := q[0].(string); ok {
				return query
			}
		}
		return ""
	}()

	if fofaQuery == "" {
		return "", errors.New("fofa query not found")
	}

	return fofaQuery, nil
}

func getShodanQuery(f string) (string, error) {
	t, err := os.ReadFile(f)
	if err != nil {
		return "", err
	}
	var tp Template
	err = yaml.Unmarshal(t, &tp)
	if err != nil {
		return "", err
	}
	shodanQuery := func() string {
		if q, ok := tp.Info.Metadata.ShodanQuery.(string); ok {
			return q
		}
		if q, ok := tp.Info.Metadata.ShodanQuery.([]interface{}); ok {
			if query, ok := q[0].(string); ok {
				return query
			}
		}
		return ""
	}()

	if shodanQuery == "" {
		return "", errors.New("shodan query not found")
	}

	return shodanQuery, nil
}

type Template struct {
	ID   string `yaml:"id"`
	Info struct {
		Metadata struct {
			FofaQuery   any `yaml:"fofa-query"`
			ShodanQuery any `yaml:"shodan-query"`
		} `yaml:"metadata"`
	} `yaml:"info"`
}
