package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
)

// 模拟响应校验示例
func main() {
	fmt.Println("🔍 WebSocket响应校验示例")
	
	// 模拟不同类型的响应
	responses := map[string]string{
		"ID命令成功": "uid=0(root) gid=0(root) groups=0(root)",
		"PS命令成功": "  PID TTY          TIME CMD\n 1234 pts/0    00:00:01 bash\n 5678 pts/0    00:00:00 ps",
		"LS命令成功": "total 12\ndrwxr-xr-x 2 <USER> <GROUP> 4096 Jan 1 12:00 bin\n-rw-r--r-- 1 <USER> <GROUP>  123 Jan 1 12:00 file.txt",
		"UNAME命令成功": "Linux hostname 5.4.0-74-generic #83-Ubuntu SMP x86_64 GNU/Linux",
		"WHOAMI命令成功": "root",
		"错误响应": "Error: command not found",
		"空响应": "",
		"权限拒绝": "Permission denied",
		"无效命令": "bash: invalid: command not found",
	}
	
	// 定义正则表达式
	regexPatterns := map[string]*regexp.Regexp{
		"ID正则": regexp.MustCompile(`uid=\d+\(.*\) gid=\d+\(.*\) groups=.*`),
		"PS正则": regexp.MustCompile(`\d+\s+\w+\/\d+\s+(\d{2}:){2}\d{2}\s+\w+`),
		"LS正则": regexp.MustCompile(`^([-drwx]{10})\s+\d+\s+\w+\s+\w+`),
		"UNAME正则": regexp.MustCompile(`(Linux|Darwin|Windows|FreeBSD|OpenBSD)`),
		"用户正则": regexp.MustCompile(`^(root|admin|user|\w+)$`),
	}
	
	fmt.Println("\n📋 测试各种响应:")
	for name, response := range responses {
		fmt.Printf("\n🔸 测试: %s\n", name)
		fmt.Printf("   响应: %s\n", truncateString(response, 50))
		
		// 测试每个正则表达式
		for regexName, regex := range regexPatterns {
			if validateWithRegex(response, regex) {
				fmt.Printf("   ✅ %s 匹配成功\n", regexName)
			}
		}
		
		// 使用综合校验
		if validateResponse(response) {
			fmt.Printf("   🎯 综合校验: 检测到漏洞\n")
		} else {
			fmt.Printf("   ❌ 综合校验: 未检测到漏洞\n")
		}
	}
	
	fmt.Println("\n🧪 测试payload解析:")
	testPayloads()
}

// validateWithRegex 使用正则表达式校验
func validateWithRegex(response string, regex *regexp.Regexp) bool {
	return regex.MatchString(response)
}

// validateResponse 综合校验响应内容
func validateResponse(response string) bool {
	if response == "" {
		return false
	}

	// 1. 检查常见的命令执行成功标识
	successIndicators := []string{
		"uid=", "gid=", "groups=",  // id命令
		"PID", "CMD", "TIME",       // ps命令
		"total", "drwx", "-rw-",    // ls命令
		"Linux", "Darwin", "Windows", "FreeBSD", // uname命令
		"root", "admin", "user",    // 用户相关
	}

	for _, indicator := range successIndicators {
		if containsIgnoreCase(response, indicator) {
			return true
		}
	}

	// 2. 检查响应长度（排除空响应和错误响应）
	if len(response) > 10 && !containsErrorKeywords(response) {
		return true
	}

	return false
}

// containsIgnoreCase 忽略大小写检查字符串包含
func containsIgnoreCase(s, substr string) bool {
	s = strings.ToLower(s)
	substr = strings.ToLower(substr)
	return strings.Contains(s, substr)
}

// containsErrorKeywords 检查是否包含错误关键词
func containsErrorKeywords(response string) bool {
	errorKeywords := []string{
		"error", "Error", "ERROR",
		"exception", "Exception", "EXCEPTION",
		"fail", "Fail", "FAIL", "failed", "Failed", "FAILED",
		"denied", "Denied", "DENIED",
		"forbidden", "Forbidden", "FORBIDDEN",
		"unauthorized", "Unauthorized", "UNAUTHORIZED",
		"not found", "Not Found", "NOT FOUND",
		"invalid", "Invalid", "INVALID",
	}

	responseLower := strings.ToLower(response)
	for _, keyword := range errorKeywords {
		if strings.Contains(responseLower, strings.ToLower(keyword)) {
			return true
		}
	}
	return false
}

// truncateString 截断字符串
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// getPayloadType 获取payload类型
func getPayloadType(payloadData []byte) string {
	var payload map[string]interface{}
	if err := json.Unmarshal(payloadData, &payload); err != nil {
		return "unknown"
	}

	if payloadType, ok := payload["type"].(string); ok {
		if data, hasData := payload["data"].(string); hasData {
			if decoded, err := base64.StdEncoding.DecodeString(data); err == nil {
				cmd := strings.TrimSpace(string(decoded))
				return fmt.Sprintf("%s(%s)", payloadType, cmd)
			}
		}
		return payloadType
	}
	return "unknown"
}

// testPayloads 测试payload解析
func testPayloads() {
	// 创建测试payload
	payloads := []map[string]interface{}{
		{
			"type": "cmd",
			"data": base64.StdEncoding.EncodeToString([]byte("id\n")),
		},
		{
			"type": "ps",
		},
		{
			"type": "cmd",
			"data": base64.StdEncoding.EncodeToString([]byte("ls -la\n")),
		},
	}
	
	for i, payload := range payloads {
		data, _ := json.Marshal(payload)
		payloadType := getPayloadType(data)
		fmt.Printf("   Payload %d: %s\n", i+1, payloadType)
	}
}

// 高级校验示例
func advancedValidationExample() {
	fmt.Println("\n🔬 高级校验示例:")
	
	// 多层校验
	response := "uid=0(root) gid=0(root) groups=0(root),1(daemon),2(bin)"
	
	// 1. 基础正则校验
	idRegex := regexp.MustCompile(`uid=\d+\(.*\) gid=\d+\(.*\) groups=.*`)
	if idRegex.MatchString(response) {
		fmt.Println("   ✅ 基础正则匹配成功")
		
		// 2. 提取具体信息
		matches := idRegex.FindStringSubmatch(response)
		if len(matches) > 0 {
			fmt.Printf("   📝 匹配内容: %s\n", matches[0])
		}
		
		// 3. 检查特权用户
		if strings.Contains(response, "uid=0(root)") {
			fmt.Println("   🚨 检测到root权限!")
		}
		
		// 4. 提取用户组信息
		if strings.Contains(response, "groups=") {
			fmt.Println("   👥 包含用户组信息")
		}
	}
}
