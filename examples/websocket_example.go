package main

import (
	"crypto/tls"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"poc-checker/pkg/webclient"
)

func main() {
	// 示例1: 基本使用
	basicExample()

	// 示例2: 自定义配置
	customConfigExample()

	// 示例3: 连接池并发
	connectionPoolExample()
}

// 基本使用示例
func basicExample() {
	fmt.Println("=== 基本使用示例 ===")

	// 使用默认配置
	config := webclient.DefaultConfig()
	client := webclient.New(config)

	// 设置消息处理器
	client.SetMessageHandler(webclient.MessageHandler{
		OnMessage: func(data []byte) {
			fmt.Printf("收到消息: %s\n", string(data))
		},
		OnError: func(err error) {
			fmt.Printf("错误: %v\n", err)
		},
		OnClose: func() {
			fmt.Println("连接已关闭")
		},
	})

	// 连接到websocket服务器
	err := client.Connect("wss://echo.websocket.org")
	if err != nil {
		log.Printf("连接失败: %v", err)
		return
	}
	defer client.Close()

	// 发送消息
	err = client.SendText("Hello WebSocket!")
	if err != nil {
		log.Printf("发送消息失败: %v", err)
		return
	}

	// 等待一段时间接收响应
	time.Sleep(2 * time.Second)
	fmt.Println()
}

// 自定义配置示例
func customConfigExample() {
	fmt.Println("=== 自定义配置示例 ===")

	// 创建自定义配置
	config := &webclient.Config{
		ConnectTimeout:     10 * time.Second,
		ReadTimeout:        30 * time.Second,
		WriteTimeout:       5 * time.Second,
		MaxRetries:         5,
		RetryInterval:      2 * time.Second,
		InsecureSkipVerify: true, // 跳过TLS验证
		Headers:            make(http.Header),
	}

	// 设置自定义请求头
	config.Headers.Set("User-Agent", "Custom-WebSocket-Client/1.0")
	config.Headers.Set("Authorization", "Bearer your-token-here")

	// 设置代理 (如果需要)
	// config.Proxy = "http://proxy.example.com:8080"

	// 设置自定义TLS配置
	config.TLSConfig = &tls.Config{
		InsecureSkipVerify: true,
		ServerName:         "example.com",
	}

	client := webclient.New(config)

	// 设置消息处理器
	client.SetMessageHandler(webclient.MessageHandler{
		OnMessage: func(data []byte) {
			fmt.Printf("自定义配置客户端收到: %s\n", string(data))
		},
		OnError: func(err error) {
			fmt.Printf("自定义配置客户端错误: %v\n", err)
		},
		OnClose: func() {
			fmt.Println("自定义配置客户端连接已关闭")
		},
	})

	// 使用重试连接
	err := client.ConnectWithRetry("wss://echo.websocket.org")
	if err != nil {
		log.Printf("重试连接失败: %v", err)
		return
	}
	defer client.Close()

	// 发送多条消息
	messages := []string{
		"消息1: 测试自定义配置",
		"消息2: 测试重试机制",
		"消息3: 测试并发发送",
	}

	for i, msg := range messages {
		err = client.SendText(fmt.Sprintf("[%d] %s", i+1, msg))
		if err != nil {
			log.Printf("发送消息 %d 失败: %v", i+1, err)
		}
		time.Sleep(500 * time.Millisecond)
	}

	time.Sleep(2 * time.Second)
	fmt.Println()
}

// 连接池并发示例
func connectionPoolExample() {
	fmt.Println("=== 连接池并发示例 ===")

	config := webclient.DefaultConfig()
	config.ConnectTimeout = 5 * time.Second

	// 创建消息处理器
	handler := webclient.MessageHandler{
		OnMessage: func(data []byte) {
			fmt.Printf("连接池收到消息: %s\n", string(data))
		},
		OnError: func(err error) {
			fmt.Printf("连接池错误: %v\n", err)
		},
		OnClose: func() {
			fmt.Println("连接池中的连接已关闭")
		},
	}

	// 创建连接池
	pool := webclient.NewConnectionPool(3, config, "wss://echo.websocket.org", handler)

	// 启动连接池
	err := pool.Start(3)
	if err != nil {
		log.Printf("启动连接池失败: %v", err)
		return
	}
	defer pool.Close()

	fmt.Println("连接池已启动，包含3个连接")

	// 并发发送消息
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			client := pool.GetClient()
			if client == nil {
				fmt.Printf("无法获取可用连接 (goroutine %d)\n", id)
				return
			}

			message := fmt.Sprintf("并发消息 %d 来自 goroutine %d", id, id)
			err := client.SendText(message)
			if err != nil {
				fmt.Printf("Goroutine %d 发送失败: %v\n", id, err)
			} else {
				fmt.Printf("Goroutine %d 发送成功: %s\n", id, message)
			}
		}(i)
	}

	wg.Wait()

	// 广播消息
	fmt.Println("\n测试广播功能...")
	err = pool.Broadcast([]byte("这是一条广播消息"))
	if err != nil {
		log.Printf("广播失败: %v", err)
	} else {
		fmt.Println("广播消息发送成功")
	}

	time.Sleep(3 * time.Second)
	fmt.Println()
}

// 代理使用示例
func proxyExample() {
	fmt.Println("=== 代理使用示例 ===")

	config := webclient.DefaultConfig()

	// 设置HTTP代理
	config.Proxy = "http://proxy.example.com:8080"

	// 或者设置SOCKS5代理
	// config.Proxy = "socks5://proxy.example.com:1080"

	client := webclient.New(config)

	client.SetMessageHandler(webclient.MessageHandler{
		OnMessage: func(data []byte) {
			fmt.Printf("通过代理收到: %s\n", string(data))
		},
		OnError: func(err error) {
			fmt.Printf("代理连接错误: %v\n", err)
		},
		OnClose: func() {
			fmt.Println("代理连接已关闭")
		},
	})

	err := client.Connect("wss://echo.websocket.org")
	if err != nil {
		log.Printf("通过代理连接失败: %v", err)
		return
	}
	defer client.Close()

	err = client.SendText("通过代理发送的消息")
	if err != nil {
		log.Printf("通过代理发送失败: %v", err)
	}

	time.Sleep(2 * time.Second)
}

// 证书使用示例
func certificateExample() {
	fmt.Println("=== 自定义证书示例 ===")

	config := webclient.DefaultConfig()

	// 加载客户端证书
	cert, err := tls.LoadX509KeyPair("client.crt", "client.key")
	if err != nil {
		log.Printf("加载客户端证书失败: %v", err)
		return
	}

	// 配置TLS
	config.TLSConfig = &tls.Config{
		Certificates: []tls.Certificate{cert},
		ServerName:   "your-server.com",
	}

	client := webclient.New(config)

	client.SetMessageHandler(webclient.MessageHandler{
		OnMessage: func(data []byte) {
			fmt.Printf("使用证书收到: %s\n", string(data))
		},
		OnError: func(err error) {
			fmt.Printf("证书连接错误: %v\n", err)
		},
		OnClose: func() {
			fmt.Println("证书连接已关闭")
		},
	})

	err = client.Connect("wss://your-secure-server.com/ws")
	if err != nil {
		log.Printf("使用证书连接失败: %v", err)
		return
	}
	defer client.Close()

	err = client.SendText("使用客户端证书发送的消息")
	if err != nil {
		log.Printf("使用证书发送失败: %v", err)
	}

	time.Sleep(2 * time.Second)
}
