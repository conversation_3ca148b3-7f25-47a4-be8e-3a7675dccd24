package utils

import (
	"bytes"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"fmt"
	"math/big"
	"time"
)

// GenerateClientCert 生成客户端认证ca证书
//
//	subj := pkix.Name{
//		CommonName: "panel_client",
//	},
func GenerateClientCert(subj pkix.Name) (*x509.CertPool, error) {
	// 生成 RSA 私钥
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, fmt.Errorf("无法生成RSA私钥: %v\n", err)
	}

	// 证书模板
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject:      subj,
		NotBefore:    time.Now(),                            // 有效期起始日期
		NotAfter:     time.Now().Add(time.Hour * 24 * 3650), // 有效期 10年

		KeyUsage:              x509.KeyUsageDigitalSignature | x509.KeyUsageKeyEncipherment,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageClientAuth}, // 客户端证书
		BasicConstraintsValid: true,
	}

	// 签名
	derBytes, err := x509.CreateCertificate(rand.Reader, &template, &template, &privateKey.PublicKey, privateKey)
	if err != nil {
		return nil, fmt.Errorf("无法创建证书: %v\n", err)
	}

	// 私钥
	keyOut := bytes.NewBuffer(nil)
	if err = pem.Encode(keyOut, &pem.Block{Type: "RSA PRIVATE KEY", Bytes: x509.MarshalPKCS1PrivateKey(privateKey)}); err != nil {
		return nil, fmt.Errorf("key编码失败: %v\n", err)
	}
	//x509Key := keyOut.Bytes()

	// 证书
	certOut := bytes.NewBuffer(nil)
	if err = pem.Encode(certOut, &pem.Block{Type: "CERTIFICATE", Bytes: derBytes}); err != nil {
		return nil, fmt.Errorf("证书编码失败: %v\n", err)
	}
	x509Cert := certOut.Bytes()

	certPool := x509.NewCertPool()
	certPool.AppendCertsFromPEM(x509Cert)

	return certPool, nil
}
