package config

import (
	"fmt"
	"io"
	"log/slog"
	"time"
)

// color codes
const (
	reset   = "\x1b[0m"
	red     = "\x1b[31m"
	green   = "\x1b[32m"
	yellow  = "\x1b[33m"
	blue    = "\x1b[34m"
	magenta = "\x1b[35m"
	cyan    = "\x1b[36m"
	white   = "\x1b[37m"
)

// levelColor 返回对应级别的颜色
func levelColor(l slog.Level) string {
	switch l {
	case slog.LevelDebug:
		return cyan
	case slog.LevelInfo:
		return green
	case slog.LevelWarn:
		return yellow
	case slog.LevelError:
		return red
	default:
		return white
	}
}

// MakeHandler 构造彩色终端 Handler
func MakeHandler(w io.Writer) slog.Handler {
	return slog.NewTextHandler(w, &slog.HandlerOptions{
		AddSource: true, // 打印调用源 file:line
		Level:     slog.LevelDebug,
		ReplaceAttr: func(groups []string, a slog.Attr) slog.Attr {
			// 给 level 上色
			if a.Key == slog.LevelKey {
				lvl := a.Value.Any().(slog.Level)
				col := levelColor(lvl)
				a.Value = slog.StringValue(fmt.Sprintf("%s%-5s%s", col, lvl.String(), reset))
			}
			// 给 time 上色
			if a.Key == slog.TimeKey {
				t := a.Value.Any().(time.Time)
				a.Value = slog.StringValue(fmt.Sprintf("%s%s%s",
					magenta,
					t.Format("2006-01-02 15:04:05"),
					reset,
				))
			}
			return a
		},
	})
}
