package webclient

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"net/url"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// Config websocket客户端配置
type Config struct {
	// 连接超时时间
	ConnectTimeout time.Duration
	// 读取超时时间
	ReadTimeout time.Duration
	// 写入超时时间
	WriteTimeout time.Duration
	// 代理地址 (支持 http://proxy:port 或 socks5://proxy:port)
	Proxy string
	// TLS配置
	TLSConfig *tls.Config
	// 自定义请求头
	Headers http.Header
	// 最大重连次数
	MaxRetries int
	// 重连间隔
	RetryInterval time.Duration
	// 是否跳过TLS验证
	InsecureSkipVerify bool
}

// WebSocketClient websocket客户端
type WebSocketClient struct {
	config *Config
	conn   *websocket.Conn
	url    string
	mu     sync.RWMutex
	closed bool
	ctx    context.Context
	cancel context.CancelFunc

	// 消息处理
	onMessage func([]byte)
	onError   func(error)
	onClose   func()

	// 并发控制
	sendChan chan []byte
	done     chan struct{}
}

// MessageHandler 消息处理器
type MessageHandler struct {
	OnMessage func([]byte)
	OnError   func(error)
	OnClose   func()
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		ConnectTimeout:     30 * time.Second,
		ReadTimeout:        60 * time.Second,
		WriteTimeout:       10 * time.Second,
		MaxRetries:         3,
		RetryInterval:      5 * time.Second,
		InsecureSkipVerify: false,
		Headers:            make(http.Header),
	}
}

// New 创建新的websocket客户端
func New(config *Config) *WebSocketClient {
	if config == nil {
		config = DefaultConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &WebSocketClient{
		config:   config,
		ctx:      ctx,
		cancel:   cancel,
		sendChan: make(chan []byte, 100), // 缓冲100条消息
		done:     make(chan struct{}),
	}
}

// SetMessageHandler 设置消息处理器
func (c *WebSocketClient) SetMessageHandler(handler MessageHandler) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.onMessage = handler.OnMessage
	c.onError = handler.OnError
	c.onClose = handler.OnClose
}

// Connect 连接到websocket服务器
func (c *WebSocketClient) Connect(wsURL string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.conn != nil {
		return fmt.Errorf("already connected")
	}

	c.url = wsURL

	// 创建dialer
	dialer := &websocket.Dialer{
		HandshakeTimeout: c.config.ConnectTimeout,
		TLSClientConfig:  c.config.TLSConfig,
	}

	// 设置代理
	if c.config.Proxy != "" {
		proxyURL, err := url.Parse(c.config.Proxy)
		if err != nil {
			return fmt.Errorf("invalid proxy URL: %v", err)
		}
		dialer.Proxy = http.ProxyURL(proxyURL)
	}

	// 设置TLS配置
	if c.config.InsecureSkipVerify {
		if dialer.TLSClientConfig == nil {
			dialer.TLSClientConfig = &tls.Config{}
		}
		dialer.TLSClientConfig.InsecureSkipVerify = true
	}

	// 建立连接
	conn, _, err := dialer.Dial(wsURL, c.config.Headers)
	if err != nil {
		return fmt.Errorf("failed to connect: %v", err)
	}

	c.conn = conn
	c.closed = false

	// 启动消息处理goroutines
	go c.readLoop()
	go c.writeLoop()

	return nil
}

// ConnectWithRetry 带重试的连接
func (c *WebSocketClient) ConnectWithRetry(wsURL string) error {
	var lastErr error

	for i := 0; i <= c.config.MaxRetries; i++ {
		if i > 0 {
			select {
			case <-c.ctx.Done():
				return c.ctx.Err()
			case <-time.After(c.config.RetryInterval):
			}
		}

		err := c.Connect(wsURL)
		if err == nil {
			return nil
		}

		lastErr = err
		if c.onError != nil {
			c.onError(fmt.Errorf("connection attempt %d failed: %v", i+1, err))
		}
	}

	return fmt.Errorf("failed to connect after %d attempts: %v", c.config.MaxRetries+1, lastErr)
}

// readLoop 读取消息循环
func (c *WebSocketClient) readLoop() {
	defer func() {
		c.mu.Lock()
		if c.conn != nil {
			c.conn.Close()
		}
		c.mu.Unlock()

		if c.onClose != nil {
			c.onClose()
		}
		close(c.done)
	}()

	for {
		select {
		case <-c.ctx.Done():
			return
		default:
		}

		c.mu.RLock()
		conn := c.conn
		closed := c.closed
		c.mu.RUnlock()

		if conn == nil || closed {
			return
		}

		// 设置读取超时
		if c.config.ReadTimeout > 0 {
			conn.SetReadDeadline(time.Now().Add(c.config.ReadTimeout))
		}

		_, message, err := conn.ReadMessage()
		if err != nil {
			if c.onError != nil {
				c.onError(fmt.Errorf("read error: %v", err))
			}
			return
		}

		if c.onMessage != nil {
			// 异步处理消息，避免阻塞读取循环
			go c.onMessage(message)
		}
	}
}

// writeLoop 写入消息循环
func (c *WebSocketClient) writeLoop() {
	for {
		select {
		case <-c.ctx.Done():
			return
		case <-c.done:
			return
		case message := <-c.sendChan:
			c.mu.RLock()
			conn := c.conn
			closed := c.closed
			c.mu.RUnlock()

			if conn == nil || closed {
				return
			}

			// 设置写入超时
			if c.config.WriteTimeout > 0 {
				conn.SetWriteDeadline(time.Now().Add(c.config.WriteTimeout))
			}

			err := conn.WriteMessage(websocket.TextMessage, message)
			if err != nil {
				if c.onError != nil {
					c.onError(fmt.Errorf("write error: %v", err))
				}
				return
			}
		}
	}
}

// SendMessage 发送消息
func (c *WebSocketClient) SendMessage(message []byte) error {
	c.mu.RLock()
	closed := c.closed
	c.mu.RUnlock()

	if closed {
		return fmt.Errorf("connection is closed")
	}

	select {
	case c.sendChan <- message:
		return nil
	case <-c.ctx.Done():
		return c.ctx.Err()
	case <-time.After(c.config.WriteTimeout):
		return fmt.Errorf("send timeout")
	}
}

// SendText 发送文本消息
func (c *WebSocketClient) SendText(text string) error {
	return c.SendMessage([]byte(text))
}

// SendBinary 发送二进制消息
func (c *WebSocketClient) SendBinary(data []byte) error {
	c.mu.RLock()
	conn := c.conn
	closed := c.closed
	c.mu.RUnlock()

	if conn == nil || closed {
		return fmt.Errorf("connection is closed")
	}

	if c.config.WriteTimeout > 0 {
		conn.SetWriteDeadline(time.Now().Add(c.config.WriteTimeout))
	}

	return conn.WriteMessage(websocket.BinaryMessage, data)
}

// Close 关闭连接
func (c *WebSocketClient) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.closed {
		return nil
	}

	c.closed = true
	c.cancel()

	if c.conn != nil {
		// 发送关闭消息
		c.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
		return c.conn.Close()
	}

	return nil
}

// IsConnected 检查是否已连接
func (c *WebSocketClient) IsConnected() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()

	return c.conn != nil && !c.closed
}

// Ping 发送ping消息
func (c *WebSocketClient) Ping() error {
	c.mu.RLock()
	conn := c.conn
	closed := c.closed
	c.mu.RUnlock()

	if conn == nil || closed {
		return fmt.Errorf("connection is closed")
	}

	if c.config.WriteTimeout > 0 {
		conn.SetWriteDeadline(time.Now().Add(c.config.WriteTimeout))
	}

	return conn.WriteMessage(websocket.PingMessage, nil)
}

// Wait 等待连接关闭
func (c *WebSocketClient) Wait() {
	<-c.done
}

// ConnectionPool websocket连接池
type ConnectionPool struct {
	clients []*WebSocketClient
	mu      sync.RWMutex
	config  *Config
	url     string
	handler MessageHandler
}

// NewConnectionPool 创建连接池
func NewConnectionPool(size int, config *Config, wsURL string, handler MessageHandler) *ConnectionPool {
	return &ConnectionPool{
		clients: make([]*WebSocketClient, 0, size),
		config:  config,
		url:     wsURL,
		handler: handler,
	}
}

// Start 启动连接池
func (p *ConnectionPool) Start(size int) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	for i := 0; i < size; i++ {
		client := New(p.config)
		client.SetMessageHandler(p.handler)

		err := client.ConnectWithRetry(p.url)
		if err != nil {
			// 关闭已创建的连接
			for _, c := range p.clients {
				c.Close()
			}
			return fmt.Errorf("failed to create connection %d: %v", i, err)
		}

		p.clients = append(p.clients, client)
	}

	return nil
}

// GetClient 获取一个可用的客户端
func (p *ConnectionPool) GetClient() *WebSocketClient {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if len(p.clients) == 0 {
		return nil
	}

	// 简单的轮询策略
	for _, client := range p.clients {
		if client.IsConnected() {
			return client
		}
	}

	return nil
}

// Broadcast 广播消息到所有连接
func (p *ConnectionPool) Broadcast(message []byte) error {
	p.mu.RLock()
	clients := make([]*WebSocketClient, len(p.clients))
	copy(clients, p.clients)
	p.mu.RUnlock()

	var wg sync.WaitGroup
	errChan := make(chan error, len(clients))

	for _, client := range clients {
		if !client.IsConnected() {
			continue
		}

		wg.Add(1)
		go func(c *WebSocketClient) {
			defer wg.Done()
			if err := c.SendMessage(message); err != nil {
				errChan <- err
			}
		}(client)
	}

	wg.Wait()
	close(errChan)

	// 收集错误
	var errors []error
	for err := range errChan {
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return fmt.Errorf("broadcast failed with %d errors: %v", len(errors), errors[0])
	}

	return nil
}

// Close 关闭连接池
func (p *ConnectionPool) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	var errors []error
	for _, client := range p.clients {
		if err := client.Close(); err != nil {
			errors = append(errors, err)
		}
	}

	p.clients = p.clients[:0]

	if len(errors) > 0 {
		return fmt.Errorf("failed to close some connections: %v", errors)
	}

	return nil
}
