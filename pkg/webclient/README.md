# WebSocket客户端

这是一个功能完整的WebSocket客户端包，支持自定义证书、代理、超时时间等配置，并提供并发连接支持。

## 特性

- ✅ 支持自定义TLS证书配置
- ✅ 支持HTTP和SOCKS5代理
- ✅ 可配置的连接、读取、写入超时时间
- ✅ 自动重连机制
- ✅ 并发消息处理
- ✅ 连接池支持
- ✅ 广播消息功能
- ✅ 异步消息处理
- ✅ 线程安全

## 安装

```bash
go get github.com/gorilla/websocket
```

## 基本使用

### 简单连接

```go
package main

import (
    "fmt"
    "log"
    "time"
    "poc-checker/pkg/webclient"
)

func main() {
    // 创建客户端
    client := webclient.New(nil) // 使用默认配置
    
    // 设置消息处理器
    client.SetMessageHandler(webclient.MessageHandler{
        OnMessage: func(data []byte) {
            fmt.Printf("收到消息: %s\n", string(data))
        },
        OnError: func(err error) {
            fmt.Printf("错误: %v\n", err)
        },
        OnClose: func() {
            fmt.Println("连接已关闭")
        },
    })
    
    // 连接到服务器
    err := client.Connect("wss://echo.websocket.org")
    if err != nil {
        log.Fatal(err)
    }
    defer client.Close()
    
    // 发送消息
    client.SendText("Hello WebSocket!")
    
    // 等待响应
    time.Sleep(2 * time.Second)
}
```

### 自定义配置

```go
config := &webclient.Config{
    ConnectTimeout:     10 * time.Second,
    ReadTimeout:        30 * time.Second,
    WriteTimeout:       5 * time.Second,
    MaxRetries:         5,
    RetryInterval:      2 * time.Second,
    InsecureSkipVerify: true,
    Headers:            make(http.Header),
}

// 设置自定义请求头
config.Headers.Set("Authorization", "Bearer your-token")
config.Headers.Set("User-Agent", "Custom-Client/1.0")

client := webclient.New(config)
```

### 代理配置

```go
config := webclient.DefaultConfig()

// HTTP代理
config.Proxy = "http://proxy.example.com:8080"

// 或者SOCKS5代理
config.Proxy = "socks5://proxy.example.com:1080"

client := webclient.New(config)
```

### TLS证书配置

```go
import "crypto/tls"

// 加载客户端证书
cert, err := tls.LoadX509KeyPair("client.crt", "client.key")
if err != nil {
    log.Fatal(err)
}

config := webclient.DefaultConfig()
config.TLSConfig = &tls.Config{
    Certificates: []tls.Certificate{cert},
    ServerName:   "your-server.com",
}

client := webclient.New(config)
```

### 重连机制

```go
// 使用重试连接
err := client.ConnectWithRetry("wss://example.com/ws")
if err != nil {
    log.Printf("连接失败: %v", err)
}
```

## 并发支持

### 连接池

```go
// 创建连接池
pool := webclient.NewConnectionPool(5, config, "wss://example.com/ws", handler)

// 启动连接池
err := pool.Start(5)
if err != nil {
    log.Fatal(err)
}
defer pool.Close()

// 获取可用连接
client := pool.GetClient()
if client != nil {
    client.SendText("消息")
}

// 广播消息到所有连接
pool.Broadcast([]byte("广播消息"))
```

### 并发发送

```go
var wg sync.WaitGroup

for i := 0; i < 100; i++ {
    wg.Add(1)
    go func(id int) {
        defer wg.Done()
        
        client := pool.GetClient()
        if client != nil {
            message := fmt.Sprintf("并发消息 %d", id)
            client.SendText(message)
        }
    }(i)
}

wg.Wait()
```

## 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| ConnectTimeout | time.Duration | 30s | 连接超时时间 |
| ReadTimeout | time.Duration | 60s | 读取超时时间 |
| WriteTimeout | time.Duration | 10s | 写入超时时间 |
| Proxy | string | "" | 代理地址 |
| TLSConfig | *tls.Config | nil | TLS配置 |
| Headers | http.Header | nil | 自定义请求头 |
| MaxRetries | int | 3 | 最大重连次数 |
| RetryInterval | time.Duration | 5s | 重连间隔 |
| InsecureSkipVerify | bool | false | 跳过TLS验证 |

## 消息处理器

```go
type MessageHandler struct {
    OnMessage func([]byte)  // 收到消息时调用
    OnError   func(error)   // 发生错误时调用
    OnClose   func()        // 连接关闭时调用
}
```

## 方法说明

### WebSocketClient方法

- `Connect(url string) error` - 连接到WebSocket服务器
- `ConnectWithRetry(url string) error` - 带重试的连接
- `SendMessage(message []byte) error` - 发送二进制消息
- `SendText(text string) error` - 发送文本消息
- `SendBinary(data []byte) error` - 发送二进制消息（直接写入）
- `Close() error` - 关闭连接
- `IsConnected() bool` - 检查连接状态
- `Ping() error` - 发送ping消息
- `Wait()` - 等待连接关闭

### ConnectionPool方法

- `Start(size int) error` - 启动指定数量的连接
- `GetClient() *WebSocketClient` - 获取可用客户端
- `Broadcast(message []byte) error` - 广播消息
- `Close() error` - 关闭连接池

## 错误处理

客户端会通过`OnError`回调函数报告各种错误：

- 连接错误
- 读取/写入错误
- 超时错误
- 网络错误

## 线程安全

- 所有公共方法都是线程安全的
- 可以在多个goroutine中同时使用
- 内部使用读写锁保护共享状态

## 示例

完整的使用示例请参考 `examples/websocket_example.go` 文件。

## 测试

运行测试：

```bash
go test ./pkg/webclient
```

## 注意事项

1. 确保在程序退出前调用`Close()`方法关闭连接
2. 消息处理器中的函数会在独立的goroutine中执行
3. 发送消息时会有缓冲，默认缓冲100条消息
4. 连接池中的连接会自动重连（如果配置了重试）
5. 使用代理时确保代理服务器支持WebSocket协议
