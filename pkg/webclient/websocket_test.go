package webclient

import (
	"crypto/tls"
	"net/http"
	"sync"
	"testing"
	"time"
)

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()
	
	if config.ConnectTimeout != 30*time.Second {
		t.Errorf("Expected ConnectTimeout to be 30s, got %v", config.ConnectTimeout)
	}
	
	if config.ReadTimeout != 60*time.Second {
		t.<PERSON>rf("Expected ReadTimeout to be 60s, got %v", config.ReadTimeout)
	}
	
	if config.WriteTimeout != 10*time.Second {
		t.Errorf("Expected WriteTimeout to be 10s, got %v", config.WriteTimeout)
	}
	
	if config.MaxRetries != 3 {
		t.<PERSON><PERSON>("Expected MaxRetries to be 3, got %d", config.MaxRetries)
	}
	
	if config.RetryInterval != 5*time.Second {
		t.Errorf("Expected RetryInterval to be 5s, got %v", config.RetryInterval)
	}
	
	if config.InsecureSkipVerify != false {
		t.<PERSON>rrorf("Expected InsecureSkipVerify to be false, got %v", config.InsecureSkipVerify)
	}
	
	if config.Headers == nil {
		t.Error("Expected Headers to be initialized")
	}
}

func TestNew(t *testing.T) {
	// 测试使用默认配置
	client := New(nil)
	if client == nil {
		t.Fatal("Expected client to be created")
	}
	
	if client.config == nil {
		t.Error("Expected config to be set")
	}
	
	if client.ctx == nil {
		t.Error("Expected context to be set")
	}
	
	if client.cancel == nil {
		t.Error("Expected cancel function to be set")
	}
	
	if client.sendChan == nil {
		t.Error("Expected sendChan to be initialized")
	}
	
	if client.done == nil {
		t.Error("Expected done channel to be initialized")
	}
	
	// 测试使用自定义配置
	customConfig := &Config{
		ConnectTimeout: 5 * time.Second,
		ReadTimeout:    10 * time.Second,
		WriteTimeout:   3 * time.Second,
		Proxy:          "http://proxy.example.com:8080",
		Headers:        make(http.Header),
	}
	
	client2 := New(customConfig)
	if client2.config != customConfig {
		t.Error("Expected custom config to be used")
	}
}

func TestSetMessageHandler(t *testing.T) {
	client := New(nil)
	
	var messageReceived bool
	var errorReceived bool
	var closeReceived bool
	
	handler := MessageHandler{
		OnMessage: func(data []byte) {
			messageReceived = true
		},
		OnError: func(err error) {
			errorReceived = true
		},
		OnClose: func() {
			closeReceived = true
		},
	}
	
	client.SetMessageHandler(handler)
	
	// 测试消息处理器是否正确设置
	if client.onMessage == nil {
		t.Error("Expected onMessage to be set")
	}
	
	if client.onError == nil {
		t.Error("Expected onError to be set")
	}
	
	if client.onClose == nil {
		t.Error("Expected onClose to be set")
	}
	
	// 测试处理器是否工作
	client.onMessage([]byte("test"))
	if !messageReceived {
		t.Error("Expected message handler to be called")
	}
	
	client.onError(nil)
	if !errorReceived {
		t.Error("Expected error handler to be called")
	}
	
	client.onClose()
	if !closeReceived {
		t.Error("Expected close handler to be called")
	}
}

func TestIsConnected(t *testing.T) {
	client := New(nil)
	
	// 初始状态应该是未连接
	if client.IsConnected() {
		t.Error("Expected client to be disconnected initially")
	}
	
	// 模拟连接状态
	client.mu.Lock()
	client.closed = false
	// 注意：这里不设置真实连接，只是测试状态检查逻辑
	client.mu.Unlock()
	
	// 没有真实连接，应该仍然返回false
	if client.IsConnected() {
		t.Error("Expected client to be disconnected without real connection")
	}
}

func TestClose(t *testing.T) {
	client := New(nil)
	
	// 测试关闭未连接的客户端
	err := client.Close()
	if err != nil {
		t.Errorf("Expected no error when closing unconnected client, got %v", err)
	}
	
	if !client.closed {
		t.Error("Expected client to be marked as closed")
	}
	
	// 测试重复关闭
	err = client.Close()
	if err != nil {
		t.Errorf("Expected no error when closing already closed client, got %v", err)
	}
}

func TestSendMessage(t *testing.T) {
	client := New(nil)
	
	// 测试在未连接状态下发送消息
	err := client.SendMessage([]byte("test"))
	if err == nil {
		t.Error("Expected error when sending message to unconnected client")
	}
	
	// 测试在关闭状态下发送消息
	client.Close()
	err = client.SendMessage([]byte("test"))
	if err == nil {
		t.Error("Expected error when sending message to closed client")
	}
}

func TestSendText(t *testing.T) {
	client := New(nil)
	
	// 测试在未连接状态下发送文本
	err := client.SendText("test message")
	if err == nil {
		t.Error("Expected error when sending text to unconnected client")
	}
}

func TestNewConnectionPool(t *testing.T) {
	config := DefaultConfig()
	handler := MessageHandler{
		OnMessage: func(data []byte) {},
		OnError:   func(err error) {},
		OnClose:   func() {},
	}
	
	pool := NewConnectionPool(5, config, "wss://example.com", handler)
	
	if pool == nil {
		t.Fatal("Expected connection pool to be created")
	}
	
	if len(pool.clients) != 0 {
		t.Error("Expected empty client list initially")
	}
	
	if pool.config != config {
		t.Error("Expected config to be set")
	}
	
	if pool.url != "wss://example.com" {
		t.Error("Expected URL to be set")
	}
}

func TestConnectionPoolClose(t *testing.T) {
	config := DefaultConfig()
	handler := MessageHandler{
		OnMessage: func(data []byte) {},
		OnError:   func(err error) {},
		OnClose:   func() {},
	}
	
	pool := NewConnectionPool(3, config, "wss://example.com", handler)
	
	// 添加一些模拟客户端
	for i := 0; i < 3; i++ {
		client := New(config)
		pool.clients = append(pool.clients, client)
	}
	
	err := pool.Close()
	if err != nil {
		t.Errorf("Expected no error when closing pool, got %v", err)
	}
	
	if len(pool.clients) != 0 {
		t.Error("Expected client list to be empty after close")
	}
}

func TestConcurrentSendMessage(t *testing.T) {
	client := New(nil)
	
	// 测试并发发送消息到未连接的客户端
	var wg sync.WaitGroup
	errorCount := 0
	var mu sync.Mutex
	
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			err := client.SendText("concurrent message")
			if err != nil {
				mu.Lock()
				errorCount++
				mu.Unlock()
			}
		}(i)
	}
	
	wg.Wait()
	
	// 所有发送都应该失败，因为客户端未连接
	if errorCount != 10 {
		t.Errorf("Expected all 10 sends to fail, got %d errors", errorCount)
	}
}

func TestConfigWithTLS(t *testing.T) {
	config := &Config{
		ConnectTimeout:     10 * time.Second,
		ReadTimeout:        30 * time.Second,
		WriteTimeout:       5 * time.Second,
		InsecureSkipVerify: true,
		TLSConfig: &tls.Config{
			ServerName: "example.com",
		},
	}
	
	client := New(config)
	
	if client.config.TLSConfig == nil {
		t.Error("Expected TLS config to be set")
	}
	
	if client.config.TLSConfig.ServerName != "example.com" {
		t.Error("Expected ServerName to be set correctly")
	}
	
	if !client.config.InsecureSkipVerify {
		t.Error("Expected InsecureSkipVerify to be true")
	}
}

func TestConfigWithProxy(t *testing.T) {
	config := &Config{
		Proxy: "http://proxy.example.com:8080",
	}
	
	client := New(config)
	
	if client.config.Proxy != "http://proxy.example.com:8080" {
		t.Error("Expected proxy to be set correctly")
	}
}

func TestConfigWithHeaders(t *testing.T) {
	headers := make(http.Header)
	headers.Set("Authorization", "Bearer token123")
	headers.Set("User-Agent", "Test-Client/1.0")
	
	config := &Config{
		Headers: headers,
	}
	
	client := New(config)
	
	if client.config.Headers.Get("Authorization") != "Bearer token123" {
		t.Error("Expected Authorization header to be set")
	}
	
	if client.config.Headers.Get("User-Agent") != "Test-Client/1.0" {
		t.Error("Expected User-Agent header to be set")
	}
}
