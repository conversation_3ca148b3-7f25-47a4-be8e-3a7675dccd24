package npoc

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"os/exec"
	"time"
)

type Npoc struct {
	exe        string
	argv       []string
	useUrlFile bool
	useUrl     bool
}

func NewNpoc() *Npoc {
	return &Npoc{}
}

func (n *Npoc) Exe(f string) *Npoc {
	n.exe = f
	return n
}

func (n *Npoc) Template(t string) *Npoc {
	n.argv = append(n.argv, "-t", t)
	return n
}

func (n *Npoc) Proxy(p string) *Npoc {
	n.argv = append(n.argv, "--proxy", p)
	return n
}

func (n *Npoc) Url(u string) *Npoc {
	n.useUrl = true
	n.argv = append(n.argv, "-u", u)
	return n
}

func (n *Npoc) UrlFile(u string) *Npoc {
	n.useUrlFile = true
	n.argv = append(n.argv, "-l", u)
	return n
}

func (n *Npoc) Run() error {
	if n.useUrl && n.useUrlFile {
		return errors.New("exec: cannot use -u with -l")
	}
	if !(n.useUrl || n.useUrlFile) {
		return errors.New("exec: need -u or -l")
	}
	cmd := exec.Command(n.exe, n.argv...)
	output, err := cmd.Output()
	if err != nil {
		return err
	}
	//fmt.Println(string(output))
	lo := &Log{}
	decoder := json.NewDecoder(bytes.NewReader(output))
	for {
		if err := decoder.Decode(lo); err != nil {
			if errors.Is(err, io.EOF) {
				break
			} else {
				return err
			}
		}
	}
	if lo.SuccessNumber >= 1 {
		return nil
	}
	return errors.New("target has no vuln")
}

type Log struct {
	Time   time.Time `json:"time"`
	Level  string    `json:"level"`
	Source struct {
		Function string `json:"function"`
		File     string `json:"file"`
		Line     int    `json:"line"`
	} `json:"source"`
	Msg           string `json:"msg"`
	SuccessNumber int    `json:"success number"`
}
