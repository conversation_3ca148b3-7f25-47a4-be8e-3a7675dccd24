package quake

import (
	"fmt"
	"time"

	"resty.dev/v3"
)

type Client struct {
	r   *resty.Client
	key string
}

type Req struct {
	Query string `json:"query"`
	Start int    `json:"start"`
	Size  int    `json:"size"`
}

type (
	Resp struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    []Data `json:"data"`
		Meta    struct {
			Pagination struct {
				Count     int `json:"count"`
				PageIndex int `json:"page_index"`
				PageSize  int `json:"page_size"`
				Total     int `json:"total"`
			} `json:"pagination"`
		} `json:"meta"`
	}
	Data struct {
		Components []struct {
			ProductLevel   string   `json:"product_level"`
			ProductType    []string `json:"product_type"`
			ProductVendor  string   `json:"product_vendor"`
			ProductNameCn  string   `json:"product_name_cn"`
			ProductNameEn  string   `json:"product_name_en"`
			Id             string   `json:"id"`
			ProductCatalog []string `json:"product_catalog"`
			Version        string   `json:"version"`
		} `json:"components"`
		Org       string `json:"org"`
		Ip        string `json:"ip"`
		IsIpv6    bool   `json:"is_ipv6"`
		Transport string `json:"transport"`
		Hostname  string `json:"hostname"`
		Port      int    `json:"port"`
		Service   struct {
			Response     string `json:"response"`
			ResponseHash string `json:"response_hash"`
			Dns          struct {
				A           []string `json:"a"`
				SupportIpv6 bool     `json:"support_ipv6"`
			} `json:"dns,omitempty"`
			Name string `json:"name"`
			Http struct {
				XPoweredBy string `json:"x_powered_by"`
				DomTree    struct {
					DomHash string `json:"dom_hash"`
					Simhash string `json:"simhash"`
				} `json:"dom_tree"`
				HeaderOrderHash string        `json:"header_order_hash"`
				Server          string        `json:"server"`
				StatusCode      int           `json:"status_code"`
				RobotsHash      string        `json:"robots_hash"`
				HttpLoadUrl     []string      `json:"http_load_url"`
				PageType        []interface{} `json:"page_type"`
				Link            struct {
					Other []struct {
						IsInner bool   `json:"is_inner"`
						Url     string `json:"url"`
					} `json:"other"`
					Script []struct {
						IsInner bool   `json:"is_inner"`
						Url     string `json:"url"`
					} `json:"script"`
				} `json:"link,omitempty"`
				HttpLoadCount int    `json:"http_load_count"`
				Body          string `json:"body"`
				MetaKeywords  string `json:"meta_keywords"`
				Title         string `json:"title"`
				SitemapHash   string `json:"sitemap_hash"`
				Path          string `json:"path"`
				Icp           struct {
					Licence     string    `json:"licence"`
					UpdateTime  time.Time `json:"update_time"`
					IsExpired   bool      `json:"is_expired"`
					LeaderName  string    `json:"leader_name"`
					Domain      string    `json:"domain"`
					MainLicence struct {
						Licence string `json:"licence"`
						Unit    string `json:"unit"`
						Nature  string `json:"nature"`
					} `json:"main_licence"`
					ContentTypeName string `json:"content_type_name"`
					LimitAccess     bool   `json:"limit_access"`
				} `json:"icp,omitempty"`
				Host          string `json:"host"`
				Robots        string `json:"robots"`
				Sitemap       string `json:"sitemap"`
				CookieElement struct {
					OrderHash string `json:"order_hash"`
					Simhash   string `json:"simhash"`
				} `json:"cookie_element"`
				Favicon struct {
					Data     string `json:"data"`
					Location string `json:"location"`
					Hash     string `json:"hash"`
					S3Url    string `json:"s3_url"`
				} `json:"favicon"`
				DataSources     int           `json:"data_sources"`
				PageTypeKeyword []interface{} `json:"page_type_keyword"`
				HtmlHash        string        `json:"html_hash"`
				ResponseHeaders string        `json:"response_headers"`
			} `json:"http"`
			Version string `json:"version"`
			Net     struct {
				ServiceProbeName string `json:"service_probe_name"`
				Tcp              struct {
					Window int `json:"window"`
				} `json:"tcp"`
				RouterIp string `json:"router_ip"`
				Ip       struct {
					Distance   int `json:"distance"`
					InitialTtl int `json:"initial_ttl"`
					Tos        int `json:"tos"`
					Ttl        int `json:"ttl"`
				} `json:"ip"`
				PortResponseTime int `json:"port_response_time"`
			} `json:"net,omitempty"`
		} `json:"service"`
		Domain   string `json:"domain,omitempty"`
		OsName   string `json:"os_name,omitempty"`
		Location struct {
			Owner       string    `json:"owner"`
			ProvinceCn  string    `json:"province_cn"`
			Isp         string    `json:"isp"`
			ProvinceEn  string    `json:"province_en"`
			CountryEn   string    `json:"country_en"`
			DistrictCn  string    `json:"district_cn"`
			Gps         []float64 `json:"gps"`
			StreetCn    string    `json:"street_cn"`
			CityEn      string    `json:"city_en"`
			DistrictEn  string    `json:"district_en"`
			CountryCn   string    `json:"country_cn"`
			StreetEn    string    `json:"street_en"`
			CityCn      string    `json:"city_cn"`
			CountryCode string    `json:"country_code"`
			Asname      string    `json:"asname"`
			SceneCn     string    `json:"scene_cn"`
			SceneEn     string    `json:"scene_en"`
			Radius      float64   `json:"radius"`
		} `json:"location"`
		Time time.Time `json:"time"`
		Asn  int       `json:"asn"`
		Id   string    `json:"id"`
	}
)

func New(key string) *Client {
	return &Client{
		r:   resty.New(),
		key: key,
	}
}

func (c *Client) Search(q string, size int) (*Resp, error) {
	resp, err := c.r.R().SetHeader("X-QuakeToken", c.key).SetBody(Req{
		Query: q,
		Start: 0,
		Size:  size,
	}).SetResult(&Resp{}).Post("https://quake.360.net/api/v3/search/quake_service")
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("status code %d", resp.StatusCode())
	}
	r := resp.Result().(*Resp)
	if r.Code != 0 {
		return nil, fmt.Errorf("code %d, msg: %s", r.Code, r.Message)
	}
	return r, nil
}
