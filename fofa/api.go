package fofa

import (
	"encoding/base64"
	"fmt"
	"strconv"

	"resty.dev/v3"
)

const api = "https://fofa.info/api"

type Resp struct {
	Err     string   `json:"err,omitempty"`
	Size    int      `json:"size,omitempty"`
	Page    int      `json:"page,omitempty"`
	Mode    string   `json:"mode,omitempty"`
	Query   string   `json:"query,omitempty"`
	Results []Result `json:"results"`
}

type Result struct {
	Host string `json:"host"`
	Ip   string `json:"ip"`
	Port int    `json:"port"`
}

type Client struct {
	c     *resty.Client
	token string
}

func New(token string) *Client {
	return &Client{
		c:     resty.New(),
		token: token,
	}
}

func (c *Client) Search(q string, size int) (*Resp, error) {
	base64Query := base64.StdEncoding.EncodeToString([]byte(q))
	resp, err := c.c.R().SetQueryParams(map[string]string{
		"key":     c.token,
		"qbase64": base64Query,
		"size":    strconv.Itoa(size),
		"fields":  "link",
		"r_type":  "json",
	}).SetResult(&Resp{}).Get("https://fofa.info/api/v1/search/all")
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("status code %d", resp.StatusCode())
	}
	return resp.Result().(*Resp), nil
}
